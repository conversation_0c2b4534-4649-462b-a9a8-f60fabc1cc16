model_list:
  - model_name: "gpt-4o-mini-openai"
    litellm_params:
      model: gpt-4o-mini
      api_key: os.environ/OPENAI_API_KEY
    model_info:
      access_groups: ["beta-models"] # 👈 Model Access Group
  - model_name: "bedrock-nova"
    litellm_params:
      model: us.amazon.nova-pro-v1:0
  - model_name: openrouter_model
    litellm_params:
      model: openrouter/openrouter_model
      api_key: os.environ/OPENROUTER_API_KEY
      api_base: http://0.0.0.0:8090
  - model_name: dall-e-3-azure
    litellm_params:
      model: azure/dall-e-3-test
      api_version: "2023-12-01-preview"
      api_base: os.environ/AZURE_SWEDEN_API_BASE
      api_key: os.environ/AZURE_SWEDEN_API_KEY
    model_info:
      input_cost_per_pixel: 10
  - model_name: "claude-3-7-sonnet"
    litellm_params:
      model: databricks/databricks-claude-3-7-sonnet
      api_key: os.environ/DATABRICKS_API_KEY
      api_base: os.environ/DATABRICKS_API_BASE
  - model_name: "gpt-4.1"
    litellm_params:
      model: azure/gpt-4.1
      api_key: os.environ/AZURE_API_KEY_REALTIME
      api_base: https://krris-m2f9a9i7-eastus2.openai.azure.com/
  - model_name: "xai/*"
    litellm_params:
      model: xai/*
      api_key: os.environ/XAI_API_KEY
  - model_name: "text-embedding-ada-002"
    litellm_params:
      model: text-embedding-ada-002
      api_key: os.environ/OPENAI_API_KEY
  - model_name: gemini/*
    litellm_params:
      model: gemini/*
  - model_name: llama-qwen
    litellm_params:
      model: ollama/qwen2:0.5b
    model_info:
      input_cost_per_token: 0.75
      output_cost_per_token: 3
  - model_name: gpt-image-1
    litellm_params:
      model: gpt-image-1
      api_key: os.environ/OPENAI_API_KEY
      # drop_params: true
  - model_name: "gpt-4o-batch"
    litellm_params:
      model: azure/gpt-4o-mini
      api_base: os.environ/AZURE_API_BASE
      api_key: os.environ/AZURE_API_KEY
    model_info: 
      id: my-general-azure-deployment
      mode: batch
  - model_name: "gpt-4o-batch"
    litellm_params:
      model: azure/gpt-4o-mini
      api_base: https://krris-m2f9a9i7-eastus2.openai.azure.com
      api_key: ********************************
    model_info: 
      id: my-unique-azure-deployment
      mode: batch
  - model_name: fake-openai-endpoint
    litellm_params:
      model: openai/fake
      api_key: fake-key
      api_base: https://exampleopenaiendpoint-production.up.railway.app/
  - model_name: "anthropic-claude-vertex"
    litellm_params:
      model: vertex_ai/claude-3-5-sonnet@20240620
      vertex_project: internal-litellm-local-dev
  - model_name: "openai-custom/*"
    litellm_params:
      model: "openai/*"
      api_key: os.environ/OPENAI_API_KEY_TEST
  - model_name: "anthropic-claude"
    litellm_params:
      model: "anthropic/claude-3-5-sonnet-latest"
      api_key: os.environ/ANTHROPIC_API_KEY

general_settings:
  store_model_in_db: true
  store_prompts_in_spend_logs: true
  token_rate_limit_type: "output"
  # master_key: os.environ/PROXY_MASTER_KEY

litellm_settings:
  # cache: true
  # cache_params:
  #   type: redis
  #   ttl: 600
  #   password: os.environ/REDIS_PASSWORD
  #   supported_call_types: ["acompletion", "completion"]
  callbacks: ["prometheus", "langfuse"]
    




