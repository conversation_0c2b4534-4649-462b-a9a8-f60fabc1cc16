<!DOCTYPE html>
<html>
<head>
    <title>Simple WebSocket Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .log { background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        button { padding: 10px 20px; margin: 5px; font-size: 16px; }
    </style>
</head>
<body>
    <h1>Simple WebSocket Test</h1>
    <button onclick="testWebSocket()">Test WebSocket Connection</button>
    <button onclick="clearLogs()">Clear Logs</button>
    
    <div id="logs"></div>

    <script>
        function log(message, type = 'info') {
            const logs = document.getElementById('logs');
            const div = document.createElement('div');
            div.className = `log ${type}`;
            div.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            logs.appendChild(div);
            console.log(message);
        }

        function clearLogs() {
            document.getElementById('logs').innerHTML = '';
        }

        function testWebSocket() {
            const sessionId = 'test-' + Date.now();
            const wsUrl = `ws://localhost:8000/ws/scrape/${sessionId}`;
            
            log(`🔌 Connecting to: ${wsUrl}`, 'info');
            
            const ws = new WebSocket(wsUrl);
            
            // Set timeout
            const timeout = setTimeout(() => {
                log('⏰ Connection timeout (10s)', 'error');
                ws.close();
            }, 10000);
            
            ws.onopen = (event) => {
                clearTimeout(timeout);
                log('✅ WebSocket connection opened!', 'success');
                
                // Send test request
                const testRequest = {
                    url: 'https://httpbin.org/html',
                    max_pages: 1,
                    delay: 0.5,
                    include_external: false,
                    scrape_whole_site: false,
                    download_content: false,
                    content_types: []
                };
                
                log(`📤 Sending test request: ${JSON.stringify(testRequest, null, 2)}`, 'info');
                ws.send(JSON.stringify(testRequest));
            };
            
            ws.onmessage = (event) => {
                try {
                    const message = JSON.parse(event.data);
                    log(`📥 Received message: ${JSON.stringify(message, null, 2)}`, 'success');
                } catch (e) {
                    log(`❌ Error parsing message: ${e}`, 'error');
                    log(`Raw message: ${event.data}`, 'info');
                }
            };
            
            ws.onclose = (event) => {
                clearTimeout(timeout);
                log(`🔌 WebSocket closed. Code: ${event.code}, Reason: ${event.reason || 'No reason provided'}`, 'info');
            };
            
            ws.onerror = (error) => {
                clearTimeout(timeout);
                log(`❌ WebSocket error: ${error}`, 'error');
                log(`Connection state: ${ws.readyState}`, 'error');
            };
        }

        // Auto-run test on page load
        window.onload = () => {
            log('🚀 WebSocket test page loaded. Click "Test WebSocket Connection" to start.', 'info');
        };
    </script>
</body>
</html>
